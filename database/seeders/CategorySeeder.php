<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Client',
                'description' => 'Regular business clients',
                'color' => '#3B82F6'
            ],
            [
                'name' => 'Vendor',
                'description' => 'Suppliers and service providers',
                'color' => '#10B981'
            ],
            [
                'name' => 'Partner',
                'description' => 'Business partners and collaborators',
                'color' => '#F59E0B'
            ],
            [
                'name' => 'Lead',
                'description' => 'Potential customers and prospects',
                'color' => '#EF4444'
            ],
            [
                'name' => 'Personal',
                'description' => 'Personal contacts',
                'color' => '#8B5CF6'
            ]
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
