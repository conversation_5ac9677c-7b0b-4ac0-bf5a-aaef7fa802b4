document.addEventListener('DOMContentLoaded', function() {
    // Initialize itemIndex based on existing rows
    const existingRows = document.querySelectorAll('.invoice-item');
    let itemIndex = existingRows.length;
    
    const itemsContainer = document.getElementById('invoice-items');
    const template = document.getElementById('invoice-item-template');
    const addButton = document.getElementById('add-item');
    
    // Add new item row
    addButton.addEventListener('click', function() {
        addItemRow();
    });
    
    // Add event listeners to existing rows
    existingRows.forEach(row => {
        row.querySelector('.item-quantity').addEventListener('input', calculateAmount);
        row.querySelector('.item-price').addEventListener('input', calculateAmount);
        row.querySelector('.remove-item').addEventListener('click', function() {
            row.remove();
            updateTotalAmount();
        });
    });
    
    // Add item row function
    function addItemRow() {
        const newRow = template.content.cloneNode(true);
        
        // Update index
        newRow.querySelectorAll('[name*="__index__"]').forEach(input => {
            input.name = input.name.replace('__index__', itemIndex);
        });
        
        // Add event listeners
        const row = newRow.querySelector('.invoice-item');
        
        // Calculate amount when quantity or price changes
        row.querySelector('.item-quantity').addEventListener('input', calculateAmount);
        row.querySelector('.item-price').addEventListener('input', calculateAmount);
        
        // Remove item
        row.querySelector('.remove-item').addEventListener('click', function() {
            row.remove();
            updateTotalAmount();
        });
        
        itemsContainer.appendChild(row);
        itemIndex++;
    }
    
    // Calculate line amount
    function calculateAmount(e) {
        const row = e.target.closest('.invoice-item');
        const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(row.querySelector('.item-price').value) || 0;
        const amount = (quantity * price).toFixed(2);
        
        row.querySelector('.item-amount').value = amount;
        updateTotalAmount();
    }
    
    // Update total invoice amount
    function updateTotalAmount() {
        let total = 0;
        document.querySelectorAll('.item-amount').forEach(input => {
            total += parseFloat(input.value) || 0;
        });
        
        document.getElementById('amount').value = total.toFixed(2);
    }
    
    // Calculate initial amounts for existing rows
    updateTotalAmount();
});
