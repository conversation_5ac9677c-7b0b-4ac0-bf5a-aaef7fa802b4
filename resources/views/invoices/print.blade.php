<x-print-layout>
    <div class="p-8 max-w-4xl mx-auto">
        <!-- Company Logo and Info -->
        <div class="flex justify-between mb-8">
            <div class="text-left">
                <!-- Bill To -->
                @if($invoice->contact)
                    <h3 class="text-gray-600 font-semibold mb-2">Invoice To:</h3>
                    <p class="font-semibold">{{ $invoice->contact->name ?? 'N/A' }}</p>
                    @if(isset($invoice->contact->address_line1) && $invoice->contact->address_line1)
                        <p>{{ $invoice->contact->address_line1 }}</p>
                    @endif
                    @if(isset($invoice->contact->address_line2) && $invoice->contact->address_line2)
                        <p>{{ $invoice->contact->address_line2 }}</p>
                    @endif
                    <p>
                        @if(isset($invoice->contact->city) && $invoice->contact->city){{ $invoice->contact->city }}@endif
                        @if(isset($invoice->contact->state) && $invoice->contact->state), {{ $invoice->contact->state }}@endif
                        @if(isset($invoice->contact->postal_code) && $invoice->contact->postal_code) {{ $invoice->contact->postal_code }}@endif
                    </p>
                    @if(isset($invoice->contact->country) && $invoice->contact->country)
                        <p>{{ $invoice->contact->country }}</p>
                    @endif
                    @if(isset($invoice->contact->email) && $invoice->contact->email)
                        <p>{{ $invoice->contact->email }}</p>
                    @endif
                    @if(isset($invoice->contact->phone) && $invoice->contact->phone)
                        <p>{{ $invoice->contact->phone }}</p>
                    @endif
                @endif
            </div>
            <div class="text-right">
                <h1 class="text-xl font-bold text-gray-800">INVOICE</h1>
                <p class="text-gray-600 text-xs">Invoice #: TD_{{ $invoice->invoice_number }}</p>
                <p class="text-gray-600 text-xs">Date: {{ $invoice->invoice_date->format('d/m/Y') }}</p>
                <p class="text-gray-600 text-xs">Due: {{ $invoice->due_date->format('d/m/Y') }}</p>
                <p class="font-semibold mt-10 px-3 py-1 inline-block rounded-full 
                    {{ $invoice->status === 'paid' ? 'bg-green-100 text-green-800' : 
                      ($invoice->status === 'overdue' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                    {{ ucfirst($invoice->status) }}
                </p>
            </div>
        </div>

        <!-- Invoice Items -->
        <div class="mb-8">
            @if($invoice->items->count() > 0)
                <table class="min-w-full border border-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase border-b">Description</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase border-b">Quantity</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase border-b">Unit Price</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase border-b">Tax</th>
                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase border-b">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($invoice->items as $item)
                            <tr class="border-b">
                                <td class="px-4 py-2 text-sm">{{ $item->description }}</td>
                                <td class="px-4 py-2 text-sm text-right">{{ number_format($item->quantity, 2) }}</td>
                                <td class="px-4 py-2 text-sm text-right">£{{ number_format($item->unit_price, 2) }}</td>
                                <td class="px-4 py-2 text-sm text-right">{{ $item->tax_class }}%</td>
                                <td class="px-4 py-2 text-sm text-right">£{{ number_format($item->amount, 2) }}</td>
                            </tr>
                        @endforeach
                        <tr class="bg-gray-50 font-bold">
                            <td colspan="4" class="px-4 py-2 text-right">Total:</td>
                            <td class="px-4 py-2 text-right">£{{ number_format($invoice->items->sum('amount'), 2) }}</td>
                        </tr>
                    </tbody>
                </table>
            @else
                <p class="text-gray-500">No items added to this invoice.</p>
            @endif
        </div>

        <div class="flex justify-between">
            <div class="text-xs">
                <h4 class="text-gray-600 font-semibold">Registered Address</h4>
                <p>7 Heron Close</p>
                <p>Bradwell</p>
                <p>Great Yarmouth</p>
                <p>Norfolk, NR31 8LY</p>
            </div>
            <div class="text-xs">
                <h4 class="text-gray-600 font-semibold">Bank Details</h4>
                <p>Bank Name: Halifax PLC</p>
                <p>Sort Code: 11-04-95</p>
                <p>Account #: ********</p>
            </div>
            <div class="text-xs">
                <h4 class="text-gray-600 font-semibold">Contact Details</h4>
                <p>Tom Davison</p>
                <p>Phone: +447823 325487</p>
                <p>Email: <EMAIL></p>
            </div>
    </div>
</x-print-layout>