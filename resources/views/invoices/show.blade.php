<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Invoice Details - TD_{{ $invoice->invoice_number }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    
                    @if($invoice->contact)
                    <div class="mb-6 flex justify-between">
                        <div class="bg-gray-50 p-4 rounded">
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Contact Information</h4>
                            <p class="font-medium">{{ $invoice->contact->name }}</p>
                            @if($invoice->contact->address_line1)
                                <p>{{ $invoice->contact->address_line1 }}</p>
                            @endif
                            @if($invoice->contact->address_line2)
                                <p>{{ $invoice->contact->address_line2 }}</p>
                            @endif
                            <p>
                                @if($invoice->contact->city){{ $invoice->contact->city }}@endif
                                @if($invoice->contact->state), {{ $invoice->contact->state }}@endif
                                @if($invoice->contact->postal_code) {{ $invoice->contact->postal_code }}@endif
                            </p>
                            @if($invoice->contact->country)
                                <p>{{ $invoice->contact->country }}</p>
                            @endif
                            @if($invoice->contact->email)
                                <p><a href="mailto:{{ $invoice->contact->email }}">{{ $invoice->contact->email }}</a></p>
                            @endif
                            @if($invoice->contact->phone)
                                <p>{{ $invoice->contact->phone }}</p>
                            @endif
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500">Invoice Date</h4>
                            <p class="text-sm mb-4">{{ $invoice->invoice_date->format('F j Y') }}</p>
                            <h4 class="text-sm font-medium text-gray-500">Due Date</h4>
                            <p class="text-sm">{{ $invoice->due_date->format('F j Y') }}</p>
                            <span class="px-3 mt-2 inline-flex text-med leading-5 font-semibold rounded-full 
                                {{ $invoice->status === 'paid' ? 'bg-green-100 text-green-800' : 
                                    ($invoice->status === 'overdue' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                                {{ ucfirst($invoice->status) }}
                            </span>
                        </div>
                    </div>
                    @endif

                    <div class="mb-6">
                        
                        @if($invoice->items->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase w-96">Description</th>
                                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Quantity</th>
                                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Unit Price</th>
                                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Tax</th>
                                            <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($invoice->items as $item)
                                            <tr>
                                                <td class="px-4 py-2 text-sm w-40">{{ $item->description }}</td>
                                                <td class="px-4 py-2 text-sm w-2 text-right">{{ number_format($item->quantity, 2) }}</td>
                                                <td class="px-4 py-2 text-sm w-2 text-right">£{{ number_format($item->unit_price, 2) }}</td>
                                                <td class="px-4 py-2 text-sm w-2 text-right">{{ $item->tax_class }}%</td>
                                                <td class="px-4 py-2 text-sm w-2 text-right">£{{ number_format($item->amount, 2) }}</td>
                                            </tr>
                                        @endforeach
                                        <tr class="bg-gray-50 font-bold">
                                            <td colspan="4" class="px-4 py-6 w-2 text-right">Total:</td>
                                            <td class="px-4 py-6 w-2 text-right">£{{ number_format($invoice->items->sum('amount'), 2) }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <p class="text-gray-500">No items added to this invoice.</p>
                        @endif
                    </div>

                    <div class="flex items-center justify-between mt-8">
                        <a href="{{ route('invoices.index') }}" class="text-gray-600 hover:text-gray-900">
                            Back to Invoices
                        </a>
                        <div class="flex space-x-4">
                            <a href="{{ route('invoices.print', $invoice) }}" target="_blank" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                                </svg>
                                Print
                            </a>
                            <a href="{{ route('invoices.edit', $invoice) }}" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                                Edit
                            </a>
                            <form action="{{ route('invoices.destroy', $invoice) }}" method="POST">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" onclick="return confirm('Are you sure you want to delete this invoice?')">
                                    Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
