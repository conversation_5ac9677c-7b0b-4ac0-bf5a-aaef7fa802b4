<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Create Invoice
        </h2>
    </x-slot>
    
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <form action="{{ route('invoices.store') }}" method="POST">
                        @csrf
                        
                        <div class="mb-4">
                            <label for="invoice_number" class="block text-sm font-medium text-gray-700">Invoice Number</label>
                            <input type="text" name="invoice_number" id="invoice_number" value="{{ old('invoice_number') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('invoice_number')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-4">
                            <label for="amount" class="block text-sm font-medium text-gray-700">Amount</label>
                            <input type="number" step="0.01" name="amount" id="amount" value="{{ old('amount') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('amount')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-4">
                            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                            <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="pending" {{ old('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="paid" {{ old('status') == 'paid' ? 'selected' : '' }}>Paid</option>
                                <option value="overdue" {{ old('status') == 'overdue' ? 'selected' : '' }}>Overdue</option>
                            </select>
                            @error('status')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="invoice_date" class="block text-sm font-medium text-gray-700">Invoice Date</label>
                            <input type="date" name="invoice_date" id="invoice_date" value="{{ old('invoice_date') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('invoice_date')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-4">
                            <label for="due_date" class="block text-sm font-medium text-gray-700">Due Date</label>
                            <input type="date" name="due_date" id="due_date" value="{{ old('due_date') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('due_date')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="contact_id" class="block text-sm font-medium text-gray-700">Contact</label>
                            <select name="contact_id" id="contact_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">Select a contact</option>
                                @foreach($contacts as $contact)
                                    <option value="{{ $contact->id }}" {{ old('contact_id') == $contact->id ? 'selected' : '' }}>
                                        {{ $contact->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('contact_id')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700">Invoice Items</label>
                            
                            <div id="invoice-items" class="mt-2">
                                <!-- Items will be added here dynamically -->
                            </div>
                            
                            <template id="invoice-item-template">
                                <div class="invoice-item grid grid-cols-12 gap-2 mb-2">
                                    <div class="col-span-4">
                                        <input type="text" name="items[__index__][description]" placeholder="Description" class="w-full rounded-md border-gray-300">
                                    </div>
                                    <div class="col-span-2">
                                        <input type="number" step="0.01" min="0.01" name="items[__index__][quantity]" placeholder="Qty" class="w-full rounded-md border-gray-300 item-quantity">
                                    </div>
                                    <div class="col-span-2">
                                        <input type="number" step="0.01" min="0" name="items[__index__][unit_price]" placeholder="Price" class="w-full rounded-md border-gray-300 item-price">
                                    </div>
                                    <div class="col-span-1">
                                        <select name="items[__index__][tax_class]" class="w-full rounded-md border-gray-300">
                                            <option value="0" selected>0%</option>
                                            <option value="20">20%</option>
                                        </select>
                                    </div>
                                    <div class="col-span-2">
                                        <input type="text" readonly name="items[__index__][amount]" placeholder="Amount" class="w-full rounded-md border-gray-300 item-amount bg-gray-100">
                                    </div>
                                    <div class="col-span-1">
                                        <button type="button" class="remove-item text-red-500 hover:text-red-700">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </template>
                            
                            <div class="grid grid-cols-12 gap-2 mb-2 text-sm font-medium text-gray-700">
                                <div class="col-span-4">Description</div>
                                <div class="col-span-2">Quantity</div>
                                <div class="col-span-2">Unit Price</div>
                                <div class="col-span-1">Tax</div>
                                <div class="col-span-2">Amount</div>
                                <div class="col-span-1"></div>
                            </div>
                            
                            <button type="button" id="add-item" class="mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                                Add Item
                            </button>
                        </div>

                        <div class="flex items-center justify-between mt-6">
                            <a href="{{ route('invoices.index') }}" class="text-gray-600 hover:text-gray-900">Cancel</a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create Invoice
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
