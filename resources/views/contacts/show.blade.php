<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Contact Details
            </h2>
            <div>
                <a href="{{ route('contacts.edit', $contact) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2">
                    Edit
                </a>
                <form action="{{ route('contacts.destroy', $contact) }}" method="POST" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" onclick="return confirm('Are you sure?')">
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </x-slot>
    
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                            
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-500">Name</h4>
                                <p class="text-lg">{{ $contact->name }}</p>
                            </div>
                            
                            @if($contact->email)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-500">Email</h4>
                                <p class="text-lg">{{ $contact->email }}</p>
                            </div>
                            @endif
                            
                            @if($contact->phone)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-500">Phone</h4>
                                <p class="text-lg">{{ $contact->phone }}</p>
                            </div>
                            @endif

                            @if($contact->category)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-500">Category</h4>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded-full mr-2" style="background-color: {{ $contact->category->color }}"></div>
                                    <span class="text-lg">{{ $contact->category->name }}</span>
                                </div>
                                @if($contact->category->description)
                                    <p class="text-sm text-gray-600 mt-1">{{ $contact->category->description }}</p>
                                @endif
                            </div>
                            @endif
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Address</h3>
                            
                            @if($contact->address_line1)
                            <div class="mb-2">
                                <p>{{ $contact->address_line1 }}</p>
                            </div>
                            @endif
                            
                            @if($contact->address_line2)
                            <div class="mb-2">
                                <p>{{ $contact->address_line2 }}</p>
                            </div>
                            @endif
                            
                            <div class="mb-2">
                                @if($contact->city){{ $contact->city }}@endif
                                @if($contact->state), {{ $contact->state }}@endif
                                @if($contact->postal_code) {{ $contact->postal_code }}@endif
                            </div>
                            
                            @if($contact->country)
                            <div class="mb-4">
                                <p>{{ $contact->country }}</p>
                            </div>
                            @endif
                        </div>
                    </div>
                    
                    @if($contact->notes)
                    <div class="mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Notes</h3>
                        <div class="bg-gray-50 p-4 rounded">
                            {{ $contact->notes }}
                        </div>
                    </div>
                    @endif
                    
                    <div class="mt-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Related Invoices</h3>
                        
                        @if($contact->invoices->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice #</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($contact->invoices as $invoice)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ $invoice->invoice_number }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ $invoice->invoice_date->format('M d, Y') }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">£{{ number_format($invoice->amount, 2) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                {{ $invoice->status === 'paid' ? 'bg-green-100 text-green-800' : 
                                                   ($invoice->status === 'overdue' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800') }}">
                                                {{ ucfirst($invoice->status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="{{ route('invoices.show', $invoice) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @else
                        <p class="text-gray-500">No invoices found for this contact.</p>
                        @endif
                    </div>
                    
                    <div class="mt-8">
                        <a href="{{ route('contacts.index') }}" class="text-indigo-600 hover:text-indigo-900">
                            &larr; Back to Contacts
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>