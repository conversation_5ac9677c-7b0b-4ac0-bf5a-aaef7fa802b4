<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <div class="flex items-center">
                    <div class="w-6 h-6 rounded-full mr-3" style="background-color: {{ $category->color }}"></div>
                    {{ $category->name }}
                </div>
            </h2>
            <div>
                <a href="{{ route('categories.edit', $category) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2">
                    Edit
                </a>
                <form action="{{ route('categories.destroy', $category) }}" method="POST" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" onclick="return confirm('Are you sure? This will remove the category from all contacts.')">
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </x-slot>
    
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Category Details</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-500">Name</h4>
                                <p class="text-lg">{{ $category->name }}</p>
                            </div>
                            
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-500">Color</h4>
                                <div class="flex items-center mt-1">
                                    <div class="w-8 h-8 rounded-full mr-3" style="background-color: {{ $category->color }}"></div>
                                    <span class="text-lg">{{ $category->color }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            @if($category->description)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-500">Description</h4>
                                <p class="text-lg">{{ $category->description }}</p>
                            </div>
                            @endif
                            
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-500">Total Contacts</h4>
                                <p class="text-lg">{{ $contacts->total() }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contacts in this category -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Contacts in this Category</h3>
                    
                    @if ($contacts->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach ($contacts as $contact)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">{{ $contact->name }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($contact->email)
                                                    <a href="mailto:{{ $contact->email }}" class="text-indigo-600 hover:text-indigo-900">{{ $contact->email }}</a>
                                                @else
                                                    <span class="text-gray-400">No email</span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">{{ $contact->phone ?: 'No phone' }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <a href="{{ route('contacts.show', $contact) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4">
                            {{ $contacts->links() }}
                        </div>
                    @else
                        <p class="text-gray-500">No contacts in this category yet.</p>
                    @endif
                    
                    <div class="mt-8">
                        <a href="{{ route('categories.index') }}" class="text-indigo-600 hover:text-indigo-900">
                            &larr; Back to Categories
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
