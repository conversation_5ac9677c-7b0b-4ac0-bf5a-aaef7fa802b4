<?php

use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\InvoiceItemController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\CategoryController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('invoices.index');
});

// Fix the dashboard route
Route::get('/dashboard', function() {
    return view('dashboard');
})->middleware(['auth'])->name('dashboard');

Route::middleware(['auth'])->group(function () {
    // Invoice routes
    Route::resource('invoices', InvoiceController::class);
    Route::get('invoices/{invoice}/print', [InvoiceController::class, 'printInvoice'])->name('invoices.print');
    
    // Contact routes
    Route::resource('contacts', ContactController::class);

    // Category routes
    Route::resource('categories', CategoryController::class);
    
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Invoice items routes
    Route::post('invoices/{invoice}/items', [InvoiceItemController::class, 'store'])->name('invoice.items.store');
    Route::delete('invoice-items/{item}', [InvoiceItemController::class, 'destroy'])->name('invoice.items.destroy');
});

require __DIR__.'/auth.php';
