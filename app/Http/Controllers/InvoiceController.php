<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Contact;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{
    public function index()
    {
        $invoices = Invoice::orderBy('invoice_number', 'desc')->paginate(10);
        return view('invoices.index', compact('invoices'));
    }

    public function create()
    {
        $contacts = Contact::orderBy('name')->get();
        return view('invoices.create', compact('contacts'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'invoice_number' => 'required|string|max:255|unique:invoices',
            'contact_id' => 'required|exists:contacts,id',
            'amount' => 'required|numeric|min:0',
            'status' => 'required|in:pending,paid,overdue',
            'due_date' => 'required|date',
            'invoice_date' => 'required|date',
            'description' => 'nullable|string|max:1000'
        ]);

        $invoice = Invoice::create($validated);
        
        if ($request->has('items')) {
            foreach ($request->items as $item) {
                $invoice->items()->create([
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'amount' => $item['quantity'] * $item['unit_price'],
                    'tax_class' => $item['tax_class'] ?? '0', // Default to 0% if not provided
                ]);
            }
        }

        return redirect()->route('invoices.index')
            ->with('success', 'Invoice created successfully.');
    }

    public function show(Invoice $invoice)
    {
        return view('invoices.show', compact('invoice'));
    }

    public function edit(Invoice $invoice)
    {
        $contacts = Contact::orderBy('name')->get();
        return view('invoices.edit', compact('invoice', 'contacts'));
    }

    public function update(Request $request, Invoice $invoice)
    {
        $validated = $request->validate([
            'invoice_number' => 'required|string|max:255|unique:invoices,invoice_number,'.$invoice->id,
            'contact_id' => 'required|exists:contacts,id',
            'amount' => 'required|numeric|min:0',
            'status' => 'required|in:pending,paid,overdue',
            'due_date' => 'required|date',
            'invoice_date' => 'required|date',
            'description' => 'nullable|string|max:1000'
        ]);

        $invoice->update($validated);
        
        // Delete existing items not in the request
        if ($request->has('items')) {
            $itemIds = collect($request->items)->pluck('id')->filter()->toArray();
            $invoice->items()->whereNotIn('id', $itemIds)->delete();
            
            // Update or create items
            foreach ($request->items as $itemData) {
                $item = isset($itemData['id']) ? 
                    $invoice->items()->find($itemData['id']) : 
                    new InvoiceItem();
                    
                $item->invoice_id = $invoice->id;
                $item->description = $itemData['description'];
                $item->quantity = $itemData['quantity'];
                $item->unit_price = $itemData['unit_price'];
                $item->amount = $itemData['quantity'] * $itemData['unit_price'];
                $item->tax_class = $itemData['tax_class'] ?? '0'; // Default to 0% if not provided
                $item->save();
            }
        } else {
            // If no items in request, delete all items
            $invoice->items()->delete();
        }

        return redirect()->route('invoices.index')
            ->with('success', 'Invoice updated successfully.');
    }

    public function destroy(Invoice $invoice)
    {
        $invoice->delete();

        return redirect()->route('invoices.index')
            ->with('success', 'Invoice deleted successfully.');
    }

    public function printInvoice(Invoice $invoice)
    {
        return view('invoices.print', compact('invoice'));
    }
}
