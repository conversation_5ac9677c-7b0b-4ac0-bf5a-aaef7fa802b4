<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use Illuminate\Http\Request;

class InvoiceItemController extends Controller
{
    public function store(Request $request, Invoice $invoice)
    {
        $validated = $request->validate([
            'description' => 'required|string|max:1000',
            'quantity' => 'required|numeric|min:0.01',
            'unit_price' => 'required|numeric|min:0',
            'tax_class' => 'required|in:0,20',
        ]);
        
        $validated['amount'] = $validated['quantity'] * $validated['unit_price'];
        $item = $invoice->items()->create($validated);
        
        return response()->json($item);
    }

    public function destroy(InvoiceItem $item)
    {
        $item->delete();
        return response()->json(['success' => true]);
    }
}
