<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'color'
    ];

    public function contacts()
    {
        return $this->hasMany(Contact::class);
    }

    public function getContactsCountAttribute()
    {
        return $this->contacts()->count();
    }
}
