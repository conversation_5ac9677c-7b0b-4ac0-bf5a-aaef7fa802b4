<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_number',
        'contact_id',
        'amount',
        'status',
        'due_date',
        'invoice_date',
        'description',
    ];

    protected $casts = [
        'due_date' => 'date',
        'invoice_date' => 'date',
        'amount' => 'decimal:2',
    ];

    public function items()
    {
        return $this->hasMany(InvoiceItem::class);
    }
    
    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }
    
}
